package com.example.audit.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Detailed audit information for individual field changes.
 * Each record represents a single field change within a database operation.
 *
 * This entity stores the before and after values for each field that was modified,
 * providing granular change tracking capabilities.
 *
 * <AUTHOR> Agent
 */
@Entity
@Table(name = "audit_details", indexes = {
    @Index(name = "idx_audit_detail_log", columnList = "audit_log_id"),
    @Index(name = "idx_audit_detail_field", columnList = "fieldName")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AuditDetail {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * Reference to the parent audit log record
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "audit_log_id", nullable = false)
    @JsonIgnore
    private AuditLog auditLog;

    /**
     * Name of the field that was changed
     */
    @Column(name = "field_name", nullable = false, length = 100)
    private String fieldName;

    /**
     * Data type of the field (for proper display formatting)
     */
    @Column(name = "field_type", length = 50)
    private String fieldType;

    /**
     * Value of the field before the change (JSON format for complex types)
     */
    @Column(name = "old_value", columnDefinition = "TEXT")
    private String oldValue;

    /**
     * Value of the field after the change (JSON format for complex types)
     */
    @Column(name = "new_value", columnDefinition = "TEXT")
    private String newValue;

    /**
     * Whether this field change is considered sensitive
     */
    @Column(name = "is_sensitive")
    @Builder.Default
    private Boolean isSensitive = false;

    /**
     * Check if this detail represents a field creation (old value is null)
     *
     * @return true if this is a field creation
     */
    public boolean isFieldCreation() {
        return oldValue == null && newValue != null;
    }

    /**
     * Check if this detail represents a field deletion (new value is null)
     *
     * @return true if this is a field deletion
     */
    public boolean isFieldDeletion() {
        return oldValue != null && newValue == null;
    }

    /**
     * Check if this detail represents a field modification (both values are not null)
     *
     * @return true if this is a field modification
     */
    public boolean isFieldModification() {
        return oldValue != null && newValue != null;
    }

    /**
     * Get a human-readable description of the change
     *
     * @return description of the change
     */
    public String getChangeDescription() {
        if (isFieldCreation()) {
            return String.format("Field '%s' was created with value: %s", fieldName, newValue);
        } else if (isFieldDeletion()) {
            return String.format("Field '%s' was deleted (previous value: %s)", fieldName, oldValue);
        } else if (isFieldModification()) {
            return String.format("Field '%s' changed from '%s' to '%s'", fieldName, oldValue, newValue);
        } else {
            return String.format("Field '%s' was accessed", fieldName);
        }
    }
}
