package com.example.audit.entity;


import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Main audit log entity that stores high-level information about database operations.
 * Each record represents a single database operation (CREATE, UPDATE, DELETE) on an entity.
 *
 * This entity is designed to be the parent record for detailed field-level changes
 * stored in the AuditDetail entity.
 *
 * <AUTHOR> Agent
 */
@Entity
@Table(name = "audit_logs", indexes = {
    @Index(name = "idx_audit_entity", columnList = "entityName, entityId"),
    @Index(name = "idx_audit_user", columnList = "userId, userNickname"),
    @Index(name = "idx_audit_timestamp", columnList = "timestamp"),
    @Index(name = "idx_audit_operation", columnList = "operation")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AuditLog {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * The name of the entity class that was modified
     */
    @Column(name = "entity_name", nullable = false, length = 100)
    private String entityName;

    /**
     * The ID of the entity instance that was modified
     */
    @Column(name = "entity_id", nullable = false, length = 50)
    private String entityId;

    /**
     * The type of operation performed (CREATE, UPDATE, DELETE)
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "operation", nullable = false, length = 10)
    private AuditOperation operation;

    /**
     * User ID from LoginUserInfo who performed the operation
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * User nickname from LoginUserInfo who performed the operation
     */
    @Column(name = "user_nickname", length = 100)
    private String userNickname;

    /**
     * Timestamp when the operation occurred
     */
    @CreationTimestamp
    @Column(name = "timestamp", nullable = false)
    private LocalDateTime timestamp;

    /**
     * Optional reason or comment for the change
     */
    @Column(name = "reason", length = 500)
    private String reason;

    /**
     * Additional context information (JSON format)
     */
    @Column(name = "context", columnDefinition = "TEXT")
    private String context;

    /**
     * IP address of the client that made the change
     */
    @Column(name = "ip_address", length = 45)
    private String ipAddress;

    /**
     * User agent of the client that made the change
     */
    @Column(name = "user_agent", length = 500)
    private String userAgent;

    /**
     * Detailed field-level changes associated with this audit log
     */
    @OneToMany(mappedBy = "auditLog", cascade = CascadeType.ALL, fetch = FetchType.LAZY)

    @Builder.Default
    private List<AuditDetail> details = new ArrayList<>();

    /**
     * Add a detail record to this audit log
     *
     * @param detail the audit detail to add
     */
    public void addDetail(AuditDetail detail) {
        details.add(detail);
        detail.setAuditLog(this);
    }

    /**
     * Enumeration of supported audit operations
     */
    public enum AuditOperation {
        CREATE, UPDATE, DELETE
    }
}
