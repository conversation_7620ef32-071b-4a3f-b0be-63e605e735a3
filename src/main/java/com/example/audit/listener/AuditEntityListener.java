package com.example.audit.listener;

import com.example.audit.entity.AuditableEntity;
import com.example.audit.service.AuditService;
import com.example.audit.util.AuditContext;
import jakarta.persistence.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;

/**
 * JPA Entity Listener that captures database operations and triggers audit logging.
 *
 * This listener is automatically invoked by JPA/Hibernate when entities are
 * persisted, updated, or removed. It integrates with the AuditService to
 * create detailed audit records with field-level change tracking.
 *
 * <AUTHOR> Agent
 */
@Component
@Slf4j
public class AuditEntityListener {

    private static AuditService auditService;

    // Thread-local storage for original entity states during updates
    private static final ThreadLocal<Map<Object, Object>> originalEntityStates =
        ThreadLocal.withInitial(ConcurrentHashMap::new);

    // Thread-local flag to prevent recursive audit calls
    private static final ThreadLocal<Boolean> auditInProgress = ThreadLocal.withInitial(() -> false);

    /**
     * Inject the audit service using setter injection to work around
     * JPA entity listener instantiation limitations
     */
    @Autowired
    public void setAuditService(AuditService auditService) {
        AuditEntityListener.auditService = auditService;
    }

    /**
     * Called before an entity is persisted to the database
     *
     * @param entity the entity being persisted
     */
    @PrePersist
    public void prePersist(Object entity) {
        log.debug("Pre-persist audit for entity: {}", entity.getClass().getSimpleName());

        if (entity instanceof AuditableEntity auditableEntity) {
            // Set audit fields
            String currentUser = AuditContext.getCurrentUserNickname();
            auditableEntity.setCreatedBy(currentUser);
            auditableEntity.setUpdatedBy(currentUser);
        }
    }

    /**
     * Called after an entity is persisted to the database
     *
     * @param entity the entity that was persisted
     */
    @PostPersist
    public void postPersist(Object entity) {
        log.debug("Post-persist audit for entity: {}", entity.getClass().getSimpleName());

        // Skip auditing for audit entities to prevent recursive loops
        if (isAuditEntity(entity)) {
            log.debug("Skipping audit for audit entity: {}", entity.getClass().getSimpleName());
            return;
        }

        // Skip if audit is already in progress to prevent recursive calls
        if (auditInProgress.get()) {
            log.debug("Audit already in progress, skipping recursive call for: {}", entity.getClass().getSimpleName());
            return;
        }

        if (auditService != null) {
            try {
                auditService.auditCreate(entity);
            } catch (Exception e) {
                log.error("Failed to audit entity creation: {}", e.getMessage(), e);
            }
        }
    }

    /**
     * Called before an entity is updated in the database
     *
     * @param entity the entity being updated
     */
    @PreUpdate
    public void preUpdate(Object entity) {
        log.debug("Pre-update audit for entity: {}", entity.getClass().getSimpleName());

        // Skip auditing for audit entities to prevent recursive loops
        if (isAuditEntity(entity)) {
            log.debug("Skipping audit for audit entity: {}", entity.getClass().getSimpleName());
            return;
        }

        // Skip if audit is already in progress to prevent recursive calls
        if (auditInProgress.get()) {
            log.debug("Audit already in progress, skipping recursive call for: {}", entity.getClass().getSimpleName());
            return;
        }

        // Store the original entity state for audit comparison
        if (auditService != null) {
            try {
                // Set flag to prevent recursive calls
                auditInProgress.set(true);

                Object originalEntity = auditService.getOriginalEntityState(entity);
                if (originalEntity != null) {
                    originalEntityStates.get().put(entity, originalEntity);
                    log.debug("Stored original entity state for: {}", entity.getClass().getSimpleName());
                } else {
                    log.warn("Original entity state is null for: {}, creating UPDATE audit immediately", entity.getClass().getSimpleName());
                    // Create UPDATE audit immediately since we can't get original state
                    // and PostUpdate might not be called if the UPDATE fails
                    log.debug("Calling auditService.auditUpdate(entity) for immediate UPDATE audit");
                    auditService.auditUpdate(entity);
                    log.debug("Completed auditService.auditUpdate(entity) call");
                }
            } catch (Exception e) {
                log.error("Failed to store original entity state: {}", e.getMessage(), e);
                // Create UPDATE audit as fallback even if there's an error
                try {
                    log.debug("Creating fallback UPDATE audit due to error");
                    auditService.auditUpdate(entity);
                    log.debug("Completed fallback UPDATE audit");
                } catch (Exception fallbackError) {
                    log.error("Failed to create fallback UPDATE audit: {}", fallbackError.getMessage(), fallbackError);
                }
            } finally {
                // Clear flag after audit operations
                auditInProgress.set(false);
            }
        } else {
            log.warn("AuditService is null in PreUpdate");
        }

        if (entity instanceof AuditableEntity auditableEntity) {
            // Set audit fields
            String currentUser = AuditContext.getCurrentUserNickname();
            auditableEntity.setUpdatedBy(currentUser);
        }
    }

    /**
     * Called after an entity is updated in the database
     *
     * @param entity the entity that was updated
     */
    @PostUpdate
    public void postUpdate(Object entity) {
        log.debug("Post-update audit for entity: {}", entity.getClass().getSimpleName());

        // Skip auditing for audit entities to prevent recursive loops
        if (isAuditEntity(entity)) {
            log.debug("Skipping audit for audit entity: {}", entity.getClass().getSimpleName());
            return;
        }

        // Skip if audit is already in progress to prevent recursive calls
        if (auditInProgress.get()) {
            log.debug("Audit already in progress, skipping recursive call for: {}", entity.getClass().getSimpleName());
            return;
        }

        if (auditService != null) {
            try {
                // Get the stored original entity state
                Object originalEntity = originalEntityStates.get().remove(entity);
                if (originalEntity != null) {
                    log.debug("Found stored original entity state for: {}", entity.getClass().getSimpleName());
                    auditService.auditUpdate(entity, originalEntity);
                } else {
                    log.warn("No stored original entity state found for: {}, creating UPDATE audit without comparison", entity.getClass().getSimpleName());
                    // Still create an UPDATE audit log even without original state
                    log.debug("Calling auditService.auditUpdate(entity) for fallback UPDATE audit");
                    auditService.auditUpdate(entity);
                    log.debug("Completed auditService.auditUpdate(entity) call");
                }
            } catch (Exception e) {
                log.error("Failed to audit entity update: {}", e.getMessage(), e);
            } finally {
                // Clean up thread-local storage if empty
                if (originalEntityStates.get().isEmpty()) {
                    originalEntityStates.remove();
                }
            }
        } else {
            log.warn("AuditService is null in PostUpdate");
        }
    }

    /**
     * Called before an entity is removed from the database
     *
     * @param entity the entity being removed
     */
    @PreRemove
    public void preRemove(Object entity) {
        log.debug("Pre-remove audit for entity: {}", entity.getClass().getSimpleName());
    }

    /**
     * Called after an entity is removed from the database
     *
     * @param entity the entity that was removed
     */
    @PostRemove
    public void postRemove(Object entity) {
        log.debug("Post-remove audit for entity: {}", entity.getClass().getSimpleName());

        // Skip auditing for audit entities to prevent recursive loops
        if (isAuditEntity(entity)) {
            log.debug("Skipping audit for audit entity: {}", entity.getClass().getSimpleName());
            return;
        }

        // Skip if audit is already in progress to prevent recursive calls
        if (auditInProgress.get()) {
            log.debug("Audit already in progress, skipping recursive call for: {}", entity.getClass().getSimpleName());
            return;
        }

        if (auditService != null) {
            try {
                auditService.auditDelete(entity);
            } catch (Exception e) {
                log.error("Failed to audit entity deletion: {}", e.getMessage(), e);
            }
        }
    }

    /**
     * Check if the entity is an audit entity to prevent recursive auditing
     *
     * @param entity the entity to check
     * @return true if the entity is an audit entity, false otherwise
     */
    private boolean isAuditEntity(Object entity) {
        String className = entity.getClass().getSimpleName();
        return "AuditLog".equals(className) || "AuditDetail".equals(className);
    }
}
