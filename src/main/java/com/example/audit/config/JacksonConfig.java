package com.example.audit.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.hibernate6.Hibernate6Module;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * Jackson configuration for handling Hibernate entities and proxies.
 * 
 * This configuration ensures that Hibernate entities can be properly
 * serialized to JSON, including handling of lazy-loaded proxies and
 * preventing serialization errors with Hibernate-specific objects.
 *
 * <AUTHOR> Agent
 */
@Configuration
public class JacksonConfig {

    /**
     * Configure ObjectMapper with Hibernate6Module to handle Hibernate proxies
     */
    @Bean
    @Primary
    public ObjectMapper objectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        
        // Register Hibernate6Module to handle Hibernate proxies
        Hibernate6Module hibernate6Module = new Hibernate6Module();
        
        // Configure the module to handle lazy loading
        hibernate6Module.disable(Hibernate6Module.Feature.USE_TRANSIENT_ANNOTATION);
        hibernate6Module.enable(Hibernate6Module.Feature.FORCE_LAZY_LOADING);
        hibernate6Module.enable(Hibernate6Module.Feature.SERIALIZE_IDENTIFIER_FOR_LAZY_NOT_LOADED_OBJECTS);
        
        mapper.registerModule(hibernate6Module);
        
        return mapper;
    }
}
