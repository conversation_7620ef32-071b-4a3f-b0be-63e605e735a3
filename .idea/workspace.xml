<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="89bd4be9-a9e1-491e-8f01-982a808cd4cc" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 6
}]]></component>
  <component name="ProjectId" id="2xkVmVjaw6BGuw7oD3o71I0Olrn" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "last_opened_file_path": "/Users/<USER>/Documents/augment-projects/jpa-auditting-demo",
    "应用程序.JpaAuditingDemoApplication.executor": "Run"
  }
}]]></component>
  <component name="RunManager">
    <configuration name="JpaAuditingDemoApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.example.audit.JpaAuditingDemoApplication" />
      <module name="jpa-auditing-demo" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.example.audit.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="应用程序.JpaAuditingDemoApplication" />
      </list>
    </recent_temporary>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="89bd4be9-a9e1-491e-8f01-982a808cd4cc" name="更改" comment="" />
      <created>1748482217629</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1748482217629</updated>
    </task>
    <servers />
  </component>
</project>