spring:
  application:
    name: jpa-auditing-demo

  datasource:
    url: jdbc:h2:mem:audit_demo;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    username: sa
    password:

  h2:
    console:
      enabled: true
      path: /h2-console

  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        use_sql_comments: true
        jdbc:
          time_zone: UTC
    open-in-view: false

  jackson:
    serialization:
      write-dates-as-timestamps: false
    time-zone: UTC

  thymeleaf:
    cache: false

server:
  port: 8080
  servlet:
    context-path: /audit-demo

logging:
  level:
    com.example.audit: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE

# Custom audit configuration
audit:
  enabled: true
  async: true
  retention-days: 365
  max-field-length: 1000
  excluded-fields:
    - password
    - token
    - secret
  user-header: x-user

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always

---
spring:
  config:
    activate:
      on-profile: test
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    username: sa
    password:
  jpa:
    hibernate:
      ddl-auto: create-drop
    database-platform: org.hibernate.dialect.H2Dialect
